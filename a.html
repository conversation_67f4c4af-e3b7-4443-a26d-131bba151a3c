<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hemodialysis System Setup</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        .overview-section {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
            border-left: 5px solid #e74c3c;
        }
        .system-diagram {
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            overflow-x: auto;
        }
        .component-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .component-card {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            border-radius: 10px;
            padding: 20px;
            border: 2px solid #17a2b8;
            transition: transform 0.3s ease;
        }
        .component-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        .component-card h3 {
            color: #2c3e50;
            margin-top: 0;
            font-size: 1.3em;
        }
        .membrane-section {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 5px solid #f39c12;
        }
        .process-section {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        .step {
            background: white;
            margin: 10px 0;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #27ae60;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .safety-warning {
            background: linear-gradient(135deg, #ff9a9e 0%, #fad0c4 100%);
            border: 2px solid #e74c3c;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            color: #721c24;
        }
        .highlight {
            background: #fff3cd;
            padding: 2px 6px;
            border-radius: 3px;
            font-weight: bold;
        }
        .section-title {
            font-size: 1.8em;
            color: #2c3e50;
            margin: 30px 0 15px 0;
            text-align: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            border-radius: 8px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }
        th {
            background: #343a40;
            color: white;
            font-weight: bold;
        }
        tr:hover {
            background: #f8f9fa;
        }
        .dual-column {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        @media (max-width: 768px) {
            .dual-column {
                grid-template-columns: 1fr;
            }
        }
        .flow-arrow {
            fill: #e74c3c;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏥 Hemodialysis System Setup</h1>
        
        <div class="overview-section">
            <h2>System Overview</h2>
            <p>Hemodialysis is a life-sustaining treatment that filters waste products, excess water, and toxins from the blood when kidneys fail to function properly. The system uses a semipermeable membrane in an artificial kidney (dialyzer) to remove uremic toxins and maintain fluid and electrolyte balance. Blood is circulated outside the body through tubing to the dialyzer and back to the patient.</p>
        </div>

        <div class="section-title">🔧 Complete Hemodialysis System Setup</div>
        
        <div class="system-diagram">
            <svg width="100%" height="700" viewBox="0 0 1400 700">
                <!-- Patient -->
                <g>
                    <ellipse cx="200" cy="350" rx="60" ry="80" fill="#ffcdd2" stroke="#c62828" stroke-width="3"/>
                    <text x="200" y="340" text-anchor="middle" font-size="14" font-weight="bold">PATIENT</text>
                    <text x="200" y="360" text-anchor="middle" font-size="10">Chronic Kidney</text>
                    <text x="200" y="375" text-anchor="middle" font-size="10">Disease</text>
                    
                    <!-- Vascular Access -->
                    <circle cx="260" cy="320" r="8" fill="#f44336" stroke="#c62828" stroke-width="2"/>
                    <text x="280" y="315" font-size="9">Arterial</text>
                    <text x="280" y="325" font-size="9">Access</text>
                    
                    <circle cx="260" cy="380" r="8" fill="#2196f3" stroke="#1976d2" stroke-width="2"/>
                    <text x="280" y="375" font-size="9">Venous</text>
                    <text x="280" y="385" font-size="9">Return</text>
                </g>

                <!-- Blood Pump -->
                <g>
                    <rect x="350" y="300" width="80" height="60" fill="#4caf50" stroke="#1b5e20" stroke-width="2" rx="5"/>
                    <text x="390" y="320" text-anchor="middle" font-size="11" font-weight="bold">Blood Pump</text>
                    <text x="390" y="335" text-anchor="middle" font-size="9">Peristaltic</text>
                    <text x="390" y="350" text-anchor="middle" font-size="9">200-400 ml/min</text>
                    
                    <!-- Pump mechanism -->
                    <circle cx="390" cy="370" r="12" fill="none" stroke="#2e7d32" stroke-width="2"/>
                    <circle cx="390" cy="370" r="3" fill="#388e3c"/>
                </g>

                <!-- Arterial Pressure Monitor -->
                <g>
                    <rect x="460" y="280" width="70" height="40" fill="#ff9800" stroke="#e65100" stroke-width="2" rx="3"/>
                    <text x="495" y="295" text-anchor="middle" font-size="9" font-weight="bold">Arterial</text>
                    <text x="495" y="308" text-anchor="middle" font-size="9">Pressure</text>
                    <text x="495" y="340" text-anchor="middle" font-size="8">-250 mmHg</text>
                </g>

                <!-- Heparin Pump -->
                <g>
                    <rect x="460" y="350" width="70" height="40" fill="#9c27b0" stroke="#4a148c" stroke-width="2" rx="3"/>
                    <text x="495" y="365" text-anchor="middle" font-size="9" font-weight="bold">Heparin</text>
                    <text x="495" y="378" text-anchor="middle" font-size="9">Pump</text>
                    <text x="495" y="405" text-anchor="middle" font-size="8">Anticoagulant</text>
                </g>

                <!-- Dialyzer (Artificial Kidney) -->
                <g>
                    <rect x="600" y="200" width="100" height="200" fill="#2196f3" stroke="#0d47a1" stroke-width="3" rx="8"/>
                    <text x="650" y="225" text-anchor="middle" font-size="12" font-weight="bold" fill="white">DIALYZER</text>
                    <text x="650" y="240" text-anchor="middle" font-size="10" fill="white">Artificial Kidney</text>
                    
                    <!-- Hollow Fiber Bundle -->
                    <rect x="620" y="250" width="60" height="120" fill="#1976d2" stroke="#0d47a1" stroke-width="1" rx="3"/>
                    <text x="650" y="265" text-anchor="middle" font-size="8" fill="white">Hollow Fiber</text>
                    <text x="650" y="275" text-anchor="middle" font-size="8" fill="white">Bundle</text>
                    
                    <!-- Fibers -->
                    <line x1="625" y1="285" x2="675" y2="285" stroke="#e3f2fd" stroke-width="1"/>
                    <line x1="625" y1="295" x2="675" y2="295" stroke="#e3f2fd" stroke-width="1"/>
                    <line x1="625" y1="305" x2="675" y2="305" stroke="#e3f2fd" stroke-width="1"/>
                    <line x1="625" y1="315" x2="675" y2="315" stroke="#e3f2fd" stroke-width="1"/>
                    <line x1="625" y1="325" x2="675" y2="325" stroke="#e3f2fd" stroke-width="1"/>
                    <line x1="625" y1="335" x2="675" y2="335" stroke="#e3f2fd" stroke-width="1"/>
                    <line x1="625" y1="345" x2="675" y2="345" stroke="#e3f2fd" stroke-width="1"/>
                    <line x1="625" y1="355" x2="675" y2="355" stroke="#e3f2fd" stroke-width="1"/>
                    
                    <!-- Blood inlet/outlet -->
                    <circle cx="590" cy="250" r="6" fill="#f44336"/>
                    <circle cx="590" cy="350" r="6" fill="#f44336"/>
                    <text x="570" y="245" font-size="8">Blood In</text>
                    <text x="565" y="360" font-size="8">Blood Out</text>
                    
                    <!-- Dialysate inlet/outlet -->
                    <circle cx="710" cy="350" r="6" fill="#4caf50"/>
                    <circle cx="710" cy="250" r="6" fill="#4caf50"/>
                    <text x="720" y="360" font-size="8">Dialysate In</text>
                    <text x="720" y="245" font-size="8">Dialysate Out</text>
                </g>

                <!-- Air Detector -->
                <g>
                    <rect x="730" y="280" width="70" height="40" fill="#ff5722" stroke="#bf360c" stroke-width="2" rx="3"/>
                    <text x="765" y="295" text-anchor="middle" font-size="9" font-weight="bold">Air</text>
                    <text x="765" y="308" text-anchor="middle" font-size="9">Detector</text>
                    <text x="765" y="340" text-anchor="middle" font-size="8">Safety</text>
                </g>

                <!-- Venous Pressure Monitor -->
                <g>
                    <rect x="830" y="280" width="70" height="40" fill="#ff9800" stroke="#e65100" stroke-width="2" rx="3"/>
                    <text x="865" y="295" text-anchor="middle" font-size="9" font-weight="bold">Venous</text>
                    <text x="865" y="308" text-anchor="middle" font-size="9">Pressure</text>
                    <text x="865" y="340" text-anchor="middle" font-size="8">+250 mmHg</text>
                </g>

                <!-- Dialysate System -->
                <g>
                    <rect x="600" y="450" width="200" height="150" fill="#4caf50" stroke="#1b5e20" stroke-width="3" rx="8"/>
                    <text x="700" y="475" text-anchor="middle" font-size="14" font-weight="bold" fill="white">DIALYSATE SYSTEM</text>
                    
                    <!-- Water Treatment -->
                    <rect x="620" y="490" width="60" height="30" fill="#2e7d32" stroke="#1b5e20" stroke-width="1" rx="3"/>
                    <text x="650" y="505" text-anchor="middle" font-size="8" fill="white">Water</text>
                    <text x="650" y="515" text-anchor="middle" font-size="8" fill="white">Treatment</text>
                    
                    <!-- Concentrate -->
                    <rect x="690" y="490" width="60" height="30" fill="#2e7d32" stroke="#1b5e20" stroke-width="1" rx="3"/>
                    <text x="720" y="505" text-anchor="middle" font-size="8" fill="white">Acid/Base</text>
                    <text x="720" y="515" text-anchor="middle" font-size="8" fill="white">Concentrate</text>
                    
                    <!-- Mixing Chamber -->
                    <rect x="620" y="530" width="60" height="30" fill="#388e3c" stroke="#1b5e20" stroke-width="1" rx="3"/>
                    <text x="650" y="545" text-anchor="middle" font-size="8" fill="white">Mixing</text>
                    <text x="650" y="555" text-anchor="middle" font-size="8" fill="white">Chamber</text>
                    
                    <!-- Temperature Control -->
                    <rect x="690" y="530" width="60" height="30" fill="#388e3c" stroke="#1b5e20" stroke-width="1" rx="3"/>
                    <text x="720" y="545" text-anchor="middle" font-size="8" fill="white">Temp</text>
                    <text x="720" y="555" text-anchor="middle" font-size="8" fill="white">Control</text>
                    
                    <!-- Dialysate pump -->
                    <circle cx="770" cy="540" r="15" fill="#66bb6a" stroke="#2e7d32" stroke-width="2"/>
                    <text x="770" y="545" text-anchor="middle" font-size="8">PUMP</text>
                    <text x="770" y="570" text-anchor="middle" font-size="8">500ml/min</text>
                </g>

                <!-- UF Collection -->
                <g>
                    <rect x="850" y="450" width="80" height="60" fill="#607d8b" stroke="#263238" stroke-width="2" rx="5"/>
                    <text x="890" y="470" text-anchor="middle" font-size="10" font-weight="bold">Ultrafiltrate</text>
                    <text x="890" y="485" text-anchor="middle" font-size="10">Collection</text>
                    <text x="890" y="525" text-anchor="middle" font-size="9">Excess Fluid</text>
                </g>

                <!-- Control Unit -->
                <g>
                    <rect x="1000" y="200" width="150" height="300" fill="#37474f" stroke="#263238" stroke-width="3" rx="8"/>
                    <text x="1075" y="230" text-anchor="middle" font-size="14" font-weight="bold" fill="white">CONTROL UNIT</text>
                    
                    <!-- Display Screen -->
                    