<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Baby Incubator and Phototherapy Unit Diagrams</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        .section-title {
            font-size: 1.8em;
            color: #2c3e50;
            margin: 30px 0 15px 0;
            text-align: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            border-radius: 8px;
        }
        .overview-section {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
            border-left: 5px solid #e74c3c;
        }
        .diagram-container {
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            overflow-x: auto;
        }
        .component-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .component-card {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            border-radius: 10px;
            padding: 20px;
            border: 2px solid #17a2b8;
            transition: transform 0.3s ease;
        }
        .component-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        .component-card h3 {
            color: #2c3e50;
            margin-top: 0;
            font-size: 1.3em;
        }
        .incubator-section {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 5px solid #f39c12;
        }
        .phototherapy-section {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 5px solid #17a2b8;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }
        th {
            background: #343a40;
            color: white;
            font-weight: bold;
        }
        tr:hover {
            background: #f8f9fa;
        }
        .dual-column {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        @media (max-width: 768px) {
            .dual-column {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>👶💡 Baby Incubator and Phototherapy Unit Diagrams</h1>

        <div class="overview-section">
            <h2>Detailed Technical Diagrams for Neonatal Care Equipment</h2>
            <p>This document provides comprehensive technical diagrams for Baby Incubators and Phototherapy Units, illustrating the detailed components, systems, and operational principles of these critical neonatal care technologies. These diagrams serve as educational tools for understanding the complex engineering and medical science behind equipment that supports the most vulnerable patients in healthcare.</p>
        </div>

        <div class="section-title">👶 Baby Incubator - Detailed System Diagram</div>

        <div class="diagram-container">
            <svg width="100%" height="900" viewBox="0 0 1200 900">
                <!-- Baby Incubator Detailed Diagram -->
                <text x="600" y="30" text-anchor="middle" font-size="20" font-weight="bold">BABY INCUBATOR - COMPLETE SYSTEM</text>

                <!-- Main incubator chamber -->
                <g>
                    <text x="600" y="80" text-anchor="middle" font-size="16" font-weight="bold">INCUBATOR CHAMBER</text>

                    <!-- Outer chamber -->
                    <rect x="300" y="100" width="600" height="300" fill="#e3f2fd" stroke="#1976d2" stroke-width="4" rx="20"/>
                    <text x="600" y="90" text-anchor="middle" font-size="12">Transparent Acrylic Chamber</text>

                    <!-- Inner environment -->
                    <rect x="320" y="120" width="560" height="260" fill="#f0f8ff" stroke="#4fc3f7" stroke-width="2" rx="15"/>

                    <!-- Baby inside -->
                    <ellipse cx="600" cy="220" rx="80" ry="40" fill="#ffcdd2" stroke="#f44336" stroke-width="3"/>
                    <circle cx="600" cy="190" r="20" fill="#ffcdd2" stroke="#f44336" stroke-width="2"/>
                    <text x="600" y="195" text-anchor="middle" font-size="10" font-weight="bold">BABY</text>

                    <!-- Mattress -->
                    <rect x="480" y="240" width="240" height="20" fill="#4caf50" stroke="#388e3c" stroke-width="2"/>
                    <text x="600" y="275" text-anchor="middle" font-size="12" font-weight="bold">Heated Mattress</text>

                    <!-- Access ports -->
                    <circle cx="350" cy="200" r="25" fill="#fff" stroke="#333" stroke-width="3"/>
                    <text x="350" y="205" text-anchor="middle" font-size="10" font-weight="bold">Access</text>
                    <text x="350" y="218" text-anchor="middle" font-size="10" font-weight="bold">Port</text>

                    <circle cx="850" cy="200" r="25" fill="#fff" stroke="#333" stroke-width="3"/>
                    <text x="850" y="205" text-anchor="middle" font-size="10" font-weight="bold">Access</text>
                    <text x="850" y="218" text-anchor="middle" font-size="10" font-weight="bold">Port</text>

                    <!-- Observation window -->
                    <rect x="400" y="110" width="400" height="10" fill="#81c784" stroke="#4caf50" stroke-width="2"/>
                    <text x="600" y="105" text-anchor="middle" font-size="10">Observation Window</text>

                    <!-- Air circulation -->
                    <path d="M 330 140 Q 400 130 470 140" stroke="#2196f3" stroke-width="3" fill="none" marker-end="url(#arrow)"/>
                    <path d="M 730 140 Q 800 130 870 140" stroke="#2196f3" stroke-width="3" fill="none" marker-end="url(#arrow)"/>
                    <text x="400" y="125" text-anchor="middle" font-size="9">Air Circulation</text>
                    <text x="800" y="125" text-anchor="middle" font-size="9">Air Circulation</text>
                </g>

                <!-- Environmental control systems -->
                <g>
                    <text x="200" y="480" text-anchor="middle" font-size="16" font-weight="bold">ENVIRONMENTAL CONTROLS</text>

                    <!-- Temperature control -->
                    <rect x="100" y="500" width="200" height="100" fill="#ffebee" stroke="#f44336" stroke-width="3"/>
                    <text x="200" y="525" text-anchor="middle" font-size="14" font-weight="bold">TEMPERATURE CONTROL</text>

                    <!-- Heating elements -->
                    <rect x="120" y="540" width="160" height="15" fill="#ff5722" stroke="#d84315" stroke-width="2"/>
                    <text x="200" y="552" text-anchor="middle" font-size="9" fill="white">Heating Elements</text>

                    <!-- Temperature sensor -->
                    <circle cx="150" cy="575" r="8" fill="#ff9800" stroke="#e65100" stroke-width="2"/>
                    <text x="150" y="580" text-anchor="middle" font-size="6" fill="white">T°</text>
                    <text x="180" y="580" text-anchor="middle" font-size="9">Temp Sensor</text>

                    <!-- Control unit -->
                    <rect x="220" y="570" width="60" height="25" fill="#2196f3" stroke="#1976d2" stroke-width="2"/>
                    <text x="250" y="585" text-anchor="middle" font-size="9" fill="white">Control</text>

                    <!-- Specifications -->
                    <text x="200" y="620" text-anchor="middle" font-size="10">Range: 32-39°C</text>
                    <text x="200" y="635" text-anchor="middle" font-size="10">Accuracy: ±0.1°C</text>
                </g>

                <!-- Humidity control -->
                <g>
                    <text x="600" y="480" text-anchor="middle" font-size="16" font-weight="bold">HUMIDITY SYSTEM</text>

                    <rect x="500" y="500" width="200" height="100" fill="#e3f2fd" stroke="#2196f3" stroke-width="3"/>
                    <text x="600" y="525" text-anchor="middle" font-size="14" font-weight="bold">HUMIDITY CONTROL</text>

                    <!-- Water reservoir -->
                    <rect x="520" y="540" width="60" height="30" fill="#81d4fa" stroke="#0288d1" stroke-width="2"/>
                    <text x="550" y="558" text-anchor="middle" font-size="9" fill="white">Water</text>
                    <text x="550" y="570" text-anchor="middle" font-size="9" fill="white">Reservoir</text>

                    <!-- Heater -->
                    <rect x="590" y="545" width="30" height="20" fill="#ff5722" stroke="#d84315" stroke-width="2"/>
                    <text x="605" y="558" text-anchor="middle" font-size="8" fill="white">Heat</text>

                    <!-- Humidity sensor -->
                    <circle cx="650" cy="555" r="8" fill="#4caf50" stroke="#388e3c" stroke-width="2"/>
                    <text x="650" y="560" text-anchor="middle" font-size="6" fill="white">H</text>
                    <text x="650" y="575" text-anchor="middle" font-size="9">Humidity</text>

                    <!-- Steam lines -->
                    <path d="M 580 545 Q 590 535 600 545" stroke="#81d4fa" stroke-width="2" fill="none"/>
                    <path d="M 590 540 Q 600 530 610 540" stroke="#81d4fa" stroke-width="2" fill="none"/>
                    <path d="M 600 535 Q 610 525 620 535" stroke="#81d4fa" stroke-width="2" fill="none"/>

                    <text x="600" y="620" text-anchor="middle" font-size="10">Range: 30-95% RH</text>
                    <text x="600" y="635" text-anchor="middle" font-size="10">Precision: ±3% RH</text>
                </g>

                <!-- Monitoring and safety -->
                <g>
                    <text x="1000" y="480" text-anchor="middle" font-size="16" font-weight="bold">MONITORING & SAFETY</text>

                    <rect x="900" y="500" width="200" height="100" fill="#e8f5e8" stroke="#4caf50" stroke-width="3"/>
                    <text x="1000" y="525" text-anchor="middle" font-size="14" font-weight="bold">SAFETY SYSTEMS</text>

                    <!-- Alarms -->
                    <rect x="920" y="540" width="50" height="20" fill="#f44336" stroke="#c62828" stroke-width="2"/>
                    <text x="945" y="553" text-anchor="middle" font-size="9" fill="white">ALARMS</text>

                    <!-- Backup systems -->
                    <rect x="980" y="540" width="50" height="20" fill="#ff9800" stroke="#e65100" stroke-width="2"/>
                    <text x="1005" y="553" text-anchor="middle" font-size="9" fill="white">BACKUP</text>

                    <!-- Battery -->
                    <rect x="950" y="570" width="40" height="15" fill="#4caf50" stroke="#388e3c" stroke-width="2"/>
                    <text x="970" y="580" text-anchor="middle" font-size="8" fill="white">BATTERY</text>

                    <text x="1000" y="620" text-anchor="middle" font-size="10">Power Failure Protection</text>
                    <text x="1000" y="635" text-anchor="middle" font-size="10">Emergency Backup</text>
                </g>

                <!-- Control panel -->
                <g>
                    <text x="600" y="680" text-anchor="middle" font-size="16" font-weight="bold">CONTROL PANEL</text>

                    <rect x="400" y="700" width="400" height="120" fill="#fff3e0" stroke="#ff9800" stroke-width="3"/>

                    <!-- Display screen -->
                    <rect x="450" y="720" width="300" height="60" fill="#000" stroke="#333" stroke-width="2"/>
                    <text x="600" y="740" text-anchor="middle" font-size="12" fill="#0f0">Temperature: 36.5°C</text>
                    <text x="600" y="755" text-anchor="middle" font-size="12" fill="#0f0">Humidity: 85% RH</text>
                    <text x="600" y="770" text-anchor="middle" font-size="12" fill="#0f0">Status: NORMAL</text>

                    <!-- Control buttons -->
                    <circle cx="430" cy="800" r="12" fill="#4caf50" stroke="#388e3c" stroke-width="2"/>
                    <text x="430" y="805" text-anchor="middle" font-size="8" fill="white">START</text>

                    <circle cx="480" cy="800" r="12" fill="#ff9800" stroke="#e65100" stroke-width="2"/>
                    <text x="480" y="805" text-anchor="middle" font-size="8" fill="white">SET</text>

                    <circle cx="530" cy="800" r="12" fill="#2196f3" stroke="#1976d2" stroke-width="2"/>
                    <text x="530" y="805" text-anchor="middle" font-size="8" fill="white">MODE</text>

                    <circle cx="670" cy="800" r="12" fill="#9c27b0" stroke="#7b1fa2" stroke-width="2"/>
                    <text x="670" y="805" text-anchor="middle" font-size="8" fill="white">MENU</text>

                    <circle cx="720" cy="800" r="12" fill="#f44336" stroke="#c62828" stroke-width="2"/>
                    <text x="720" y="805" text-anchor="middle" font-size="8" fill="white">STOP</text>

                    <circle cx="770" cy="800" r="12" fill="#607d8b" stroke="#455a64" stroke-width="2"/>
                    <text x="770" y="805" text-anchor="middle" font-size="8" fill="white">MUTE</text>
                </g>

                <!-- Connection lines -->
                <path d="M 200 600 L 400 300" stroke="#f44336" stroke-width="3" fill="none" marker-end="url(#arrow)"/>
                <path d="M 600 600 L 600 400" stroke="#2196f3" stroke-width="3" fill="none" marker-end="url(#arrow)"/>
                <path d="M 1000 600 L 800 300" stroke="#4caf50" stroke-width="3" fill="none" marker-end="url(#arrow)"/>

                <!-- Arrow marker -->
                <defs>
                    <marker id="arrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                        <polygon points="0 0, 10 3.5, 0 7" fill="#333"/>
                    </marker>
                </defs>
            </svg>
        </div>

        <div class="section-title">💡 Phototherapy Unit - Detailed System Diagram</div>

        <div class="diagram-container">
            <svg width="100%" height="1000" viewBox="0 0 1200 1000">
                <!-- Phototherapy Unit Detailed Diagram -->
                <text x="600" y="30" text-anchor="middle" font-size="20" font-weight="bold">PHOTOTHERAPY UNIT - COMPLETE SYSTEM</text>

                <!-- LED Phototherapy System -->
                <g>
                    <text x="300" y="80" text-anchor="middle" font-size="16" font-weight="bold">LED PHOTOTHERAPY SYSTEM</text>

                    <!-- LED Panel Housing -->
                    <rect x="150" y="100" width="300" height="150" fill="#fff3e0" stroke="#ff9800" stroke-width="4" rx="15"/>
                    <text x="300" y="90" text-anchor="middle" font-size="12">LED Panel Housing</text>

                    <!-- LED Array -->
                    <text x="300" y="125" text-anchor="middle" font-size="12" font-weight="bold">LED ARRAY</text>

                    <!-- Individual LEDs -->
                    <circle cx="200" cy="150" r="12" fill="#2196f3" stroke="#1976d2" stroke-width="2"/>
                    <text x="200" y="155" text-anchor="middle" font-size="8" fill="white">LED</text>
                    <circle cx="250" cy="150" r="12" fill="#2196f3" stroke="#1976d2" stroke-width="2"/>
                    <text x="250" y="155" text-anchor="middle" font-size="8" fill="white">LED</text>
                    <circle cx="300" cy="150" r="12" fill="#2196f3" stroke="#1976d2" stroke-width="2"/>
                    <text x="300" y="155" text-anchor="middle" font-size="8" fill="white">LED</text>
                    <circle cx="350" cy="150" r="12" fill="#2196f3" stroke="#1976d2" stroke-width="2"/>
                    <text x="350" y="155" text-anchor="middle" font-size="8" fill="white">LED</text>
                    <circle cx="400" cy="150" r="12" fill="#2196f3" stroke="#1976d2" stroke-width="2"/>
                    <text x="400" y="155" text-anchor="middle" font-size="8" fill="white">LED</text>

                    <circle cx="200" cy="190" r="12" fill="#2196f3" stroke="#1976d2" stroke-width="2"/>
                    <text x="200" y="195" text-anchor="middle" font-size="8" fill="white">LED</text>
                    <circle cx="250" cy="190" r="12" fill="#2196f3" stroke="#1976d2" stroke-width="2"/>
                    <text x="250" y="195" text-anchor="middle" font-size="8" fill="white">LED</text>
                    <circle cx="300" cy="190" r="12" fill="#2196f3" stroke="#1976d2" stroke-width="2"/>
                    <text x="300" y="195" text-anchor="middle" font-size="8" fill="white">LED</text>
                    <circle cx="350" cy="190" r="12" fill="#2196f3" stroke="#1976d2" stroke-width="2"/>
                    <text x="350" y="195" text-anchor="middle" font-size="8" fill="white">LED</text>
                    <circle cx="400" cy="190" r="12" fill="#2196f3" stroke="#1976d2" stroke-width="2"/>
                    <text x="400" y="195" text-anchor="middle" font-size="8" fill="white">LED</text>

                    <circle cx="200" cy="230" r="12" fill="#2196f3" stroke="#1976d2" stroke-width="2"/>
                    <text x="200" y="235" text-anchor="middle" font-size="8" fill="white">LED</text>
                    <circle cx="250" cy="230" r="12" fill="#2196f3" stroke="#1976d2" stroke-width="2"/>
                    <text x="250" y="235" text-anchor="middle" font-size="8" fill="white">LED</text>
                    <circle cx="300" cy="230" r="12" fill="#2196f3" stroke="#1976d2" stroke-width="2"/>
                    <text x="300" y="235" text-anchor="middle" font-size="8" fill="white">LED</text>
                    <circle cx="350" cy="230" r="12" fill="#2196f3" stroke="#1976d2" stroke-width="2"/>
                    <text x="350" y="235" text-anchor="middle" font-size="8" fill="white">LED</text>
                    <circle cx="400" cy="230" r="12" fill="#2196f3" stroke="#1976d2" stroke-width="2"/>
                    <text x="400" y="235" text-anchor="middle" font-size="8" fill="white">LED</text>

                    <!-- Light rays -->
                    <path d="M 200 162 L 200 280" stroke="#ffeb3b" stroke-width="4" fill="none"/>
                    <path d="M 250 162 L 250 280" stroke="#ffeb3b" stroke-width="4" fill="none"/>
                    <path d="M 300 162 L 300 280" stroke="#ffeb3b" stroke-width="4" fill="none"/>
                    <path d="M 350 162 L 350 280" stroke="#ffeb3b" stroke-width="4" fill="none"/>
                    <path d="M 400 162 L 400 280" stroke="#ffeb3b" stroke-width="4" fill="none"/>

                    <path d="M 200 202 L 200 280" stroke="#ffeb3b" stroke-width="4" fill="none"/>
                    <path d="M 250 202 L 250 280" stroke="#ffeb3b" stroke-width="4" fill="none"/>
                    <path d="M 300 202 L 300 280" stroke="#ffeb3b" stroke-width="4" fill="none"/>
                    <path d="M 350 202 L 350 280" stroke="#ffeb3b" stroke-width="4" fill="none"/>
                    <path d="M 400 202 L 400 280" stroke="#ffeb3b" stroke-width="4" fill="none"/>

                    <path d="M 200 242 L 200 280" stroke="#ffeb3b" stroke-width="4" fill="none"/>
                    <path d="M 250 242 L 250 280" stroke="#ffeb3b" stroke-width="4" fill="none"/>
                    <path d="M 300 242 L 300 280" stroke="#ffeb3b" stroke-width="4" fill="none"/>
                    <path d="M 350 242 L 350 280" stroke="#ffeb3b" stroke-width="4" fill="none"/>
                    <path d="M 400 242 L 400 280" stroke="#ffeb3b" stroke-width="4" fill="none"/>

                    <!-- Treatment area -->
                    <rect x="180" y="280" width="240" height="60" fill="#ffebee" stroke="#f44336" stroke-width="3" rx="10"/>
                    <text x="300" y="300" text-anchor="middle" font-size="12" font-weight="bold">TREATMENT AREA</text>

                    <!-- Baby under treatment -->
                    <ellipse cx="300" cy="320" rx="60" ry="25" fill="#ffcdd2" stroke="#f44336" stroke-width="2"/>
                    <circle cx="300" cy="305" r="12" fill="#ffcdd2" stroke="#f44336" stroke-width="1"/>
                    <text x="300" y="310" text-anchor="middle" font-size="8">Baby</text>

                    <!-- Eye protection -->
                    <rect x="290" y="300" width="20" height="8" fill="#333" stroke="#000" stroke-width="1"/>
                    <text x="300" y="295" text-anchor="middle" font-size="8">Eye Shield</text>
                </g>

                <!-- Fiber Optic System -->
                <g>
                    <text x="800" y="80" text-anchor="middle" font-size="16" font-weight="bold">FIBER OPTIC SYSTEM</text>

                    <!-- Light source unit -->
                    <rect x="700" y="100" width="200" height="100" fill="#e1bee7" stroke="#8e24aa" stroke-width="4" rx="15"/>
                    <text x="800" y="125" text-anchor="middle" font-size="14" font-weight="bold">LIGHT SOURCE</text>

                    <!-- Halogen/LED lamp -->
                    <circle cx="750" cy="150" r="20" fill="#ffeb3b" stroke="#f57c00" stroke-width="3"/>
                    <text x="750" y="155" text-anchor="middle" font-size="10" font-weight="bold">LAMP</text>

                    <!-- Reflector -->
                    <path d="M 720 130 Q 750 120 780 130" stroke="#c0c0c0" stroke-width="4" fill="none"/>
                    <text x="750" y="115" text-anchor="middle" font-size="9">Reflector</text>

                    <!-- Filter -->
                    <rect x="820" y="140" width="30" height="20" fill="#81c784" stroke="#4caf50" stroke-width="2"/>
                    <text x="835" y="153" text-anchor="middle" font-size="8" fill="white">Filter</text>

                    <!-- Fiber optic cable -->
                    <path d="M 800 200 Q 850 220 900 250 Q 950 280 1000 320" stroke="#ff9800" stroke-width="8" fill="none"/>
                    <text x="900" y="240" text-anchor="middle" font-size="12" font-weight="bold">FIBER OPTIC CABLE</text>

                    <!-- Cable details -->
                    <circle cx="850" cy="230" r="4" fill="#ff5722"/>
                    <circle cx="900" cy="260" r="4" fill="#ff5722"/>
                    <circle cx="950" cy="290" r="4" fill="#ff5722"/>

                    <!-- Phototherapy blanket -->
                    <rect x="950" y="320" width="150" height="80" fill="#c8e6c9" stroke="#4caf50" stroke-width="3" rx="10"/>
                    <text x="1025" y="345" text-anchor="middle" font-size="14" font-weight="bold">PHOTOTHERAPY</text>
                    <text x="1025" y="365" text-anchor="middle" font-size="14" font-weight="bold">BLANKET</text>

                    <!-- Fiber distribution in blanket -->
                    <path d="M 960 380 L 1090 380" stroke="#ffeb3b" stroke-width="2" fill="none"/>
                    <path d="M 960 385 L 1090 385" stroke="#ffeb3b" stroke-width="2" fill="none"/>
                    <path d="M 960 390 L 1090 390" stroke="#ffeb3b" stroke-width="2" fill="none"/>
                    <text x="1025" y="375" text-anchor="middle" font-size="9">Light Distribution</text>

                    <!-- Baby with blanket -->
                    <ellipse cx="1025" cy="430" rx="60" ry="25" fill="#ffcdd2" stroke="#f44336" stroke-width="2"/>
                    <circle cx="1025" cy="415" r="12" fill="#ffcdd2" stroke="#f44336" stroke-width="1"/>
                    <text x="1025" y="420" text-anchor="middle" font-size="8">Baby</text>
                    <text x="1025" y="460" text-anchor="middle" font-size="10">with Blanket</text>
                </g>

                <!-- Wavelength spectrum analysis -->
                <g>
                    <text x="600" y="520" text-anchor="middle" font-size="16" font-weight="bold">WAVELENGTH SPECTRUM ANALYSIS</text>

                    <!-- Spectrum display -->
                    <rect x="200" y="540" width="800" height="60" fill="url(#spectrum)" stroke="#333" stroke-width="3"/>
                    <text x="600" y="535" text-anchor="middle" font-size="12">Therapeutic Light Spectrum</text>

                    <!-- Wavelength markers -->
                    <line x1="300" y1="530" x2="300" y2="610" stroke="#333" stroke-width="3"/>
                    <text x="300" y="625" text-anchor="middle" font-size="11" font-weight="bold">400nm</text>
                    <text x="300" y="640" text-anchor="middle" font-size="10">Violet</text>

                    <line x1="450" y1="530" x2="450" y2="610" stroke="#f44336" stroke-width="4"/>
                    <text x="450" y="625" text-anchor="middle" font-size="11" font-weight="bold">460nm</text>
                    <text x="450" y="640" text-anchor="middle" font-size="10" font-weight="bold">PEAK BLUE</text>

                    <line x1="600" y1="530" x2="600" y2="610" stroke="#333" stroke-width="3"/>
                    <text x="600" y="625" text-anchor="middle" font-size="11" font-weight="bold">490nm</text>
                    <text x="600" y="640" text-anchor="middle" font-size="10">Blue-Green</text>

                    <line x1="750" y1="530" x2="750" y2="610" stroke="#333" stroke-width="3"/>
                    <text x="750" y="625" text-anchor="middle" font-size="11" font-weight="bold">550nm</text>
                    <text x="750" y="640" text-anchor="middle" font-size="10">Green</text>

                    <line x1="900" y1="530" x2="900" y2="610" stroke="#333" stroke-width="3"/>
                    <text x="900" y="625" text-anchor="middle" font-size="11" font-weight="bold">600nm</text>
                    <text x="900" y="640" text-anchor="middle" font-size="10">Orange</text>

                    <!-- Optimal therapeutic range -->
                    <rect x="400" y="520" width="250" height="80" fill="none" stroke="#f44336" stroke-width="4" stroke-dasharray="10,5"/>
                    <text x="525" y="515" text-anchor="middle" font-size="14" font-weight="bold" fill="#f44336">OPTIMAL THERAPEUTIC RANGE</text>
                    <text x="525" y="655" text-anchor="middle" font-size="12" font-weight="bold">430-490 nm</text>
                </g>

                <!-- Control and monitoring systems -->
                <g>
                    <text x="300" y="700" text-anchor="middle" font-size="16" font-weight="bold">CONTROL SYSTEMS</text>

                    <!-- Control panel -->
                    <rect x="150" y="720" width="300" height="120" fill="#e3f2fd" stroke="#2196f3" stroke-width="3" rx="10"/>
                    <text x="300" y="745" text-anchor="middle" font-size="14" font-weight="bold">CONTROL PANEL</text>

                    <!-- Digital display -->
                    <rect x="180" y="760" width="240" height="40" fill="#000" stroke="#333" stroke-width="2"/>
                    <text x="300" y="775" text-anchor="middle" font-size="11" fill="#0f0">Irradiance: 35 μW/cm²/nm</text>
                    <text x="300" y="790" text-anchor="middle" font-size="11" fill="#0f0">Timer: 02:45:30</text>

                    <!-- Control buttons -->
                    <circle cx="200" cy="820" r="10" fill="#4caf50" stroke="#388e3c" stroke-width="2"/>
                    <text x="200" y="825" text-anchor="middle" font-size="7" fill="white">ON</text>

                    <circle cx="240" cy="820" r="10" fill="#f44336" stroke="#c62828" stroke-width="2"/>
                    <text x="240" y="825" text-anchor="middle" font-size="7" fill="white">OFF</text>

                    <circle cx="280" cy="820" r="10" fill="#ff9800" stroke="#e65100" stroke-width="2"/>
                    <text x="280" y="825" text-anchor="middle" font-size="7" fill="white">SET</text>

                    <circle cx="320" cy="820" r="10" fill="#2196f3" stroke="#1976d2" stroke-width="2"/>
                    <text x="320" y="825" text-anchor="middle" font-size="7" fill="white">MODE</text>

                    <circle cx="360" cy="820" r="10" fill="#9c27b0" stroke="#7b1fa2" stroke-width="2"/>
                    <text x="360" y="825" text-anchor="middle" font-size="7" fill="white">MENU</text>

                    <circle cx="400" cy="820" r="10" fill="#607d8b" stroke="#455a64" stroke-width="2"/>
                    <text x="400" y="825" text-anchor="middle" font-size="7" fill="white">MUTE</text>
                </g>

                <!-- Safety and monitoring -->
                <g>
                    <text x="800" y="700" text-anchor="middle" font-size="16" font-weight="bold">SAFETY & MONITORING</text>

                    <rect x="650" y="720" width="300" height="120" fill="#fff3e0" stroke="#ff9800" stroke-width="3" rx="10"/>
                    <text x="800" y="745" text-anchor="middle" font-size="14" font-weight="bold">SAFETY SYSTEMS</text>

                    <!-- Irradiance meter -->
                    <rect x="680" y="760" width="80" height="30" fill="#4caf50" stroke="#388e3c" stroke-width="2"/>
                    <text x="720" y="778" text-anchor="middle" font-size="10" fill="white">Irradiance</text>
                    <text x="720" y="788" text-anchor="middle" font-size="10" fill="white">Meter</text>

                    <!-- Temperature monitor -->
                    <rect x="780" y="760" width="80" height="30" fill="#f44336" stroke="#c62828" stroke-width="2"/>
                    <text x="820" y="778" text-anchor="middle" font-size="10" fill="white">Temperature</text>
                    <text x="820" y="788" text-anchor="middle" font-size="10" fill="white">Monitor</text>

                    <!-- Timer system -->
                    <rect x="680" y="800" width="80" height="30" fill="#2196f3" stroke="#1976d2" stroke-width="2"/>
                    <text x="720" y="818" text-anchor="middle" font-size="10" fill="white">Timer</text>
                    <text x="720" y="828" text-anchor="middle" font-size="10" fill="white">System</text>

                    <!-- Alarm system -->
                    <rect x="780" y="800" width="80" height="30" fill="#9c27b0" stroke="#7b1fa2" stroke-width="2"/>
                    <text x="820" y="818" text-anchor="middle" font-size="10" fill="white">Alarm</text>
                    <text x="820" y="828" text-anchor="middle" font-size="10" fill="white">System</text>
                </g>

                <!-- Performance specifications -->
                <g>
                    <text x="600" y="880" text-anchor="middle" font-size="16" font-weight="bold">PERFORMANCE SPECIFICATIONS</text>

                    <text x="200" y="910" font-size="12" font-weight="bold">LED System:</text>
                    <text x="200" y="925" font-size="11">• Irradiance: 30-50 μW/cm²/nm</text>
                    <text x="200" y="940" font-size="11">• Wavelength: 460±15 nm</text>
                    <text x="200" y="955" font-size="11">• Coverage: 30×40 cm</text>
                    <text x="200" y="970" font-size="11">• Distance: 35-45 cm</text>

                    <text x="500" y="910" font-size="12" font-weight="bold">Fiber Optic:</text>
                    <text x="500" y="925" font-size="11">• Irradiance: 15-30 μW/cm²/nm</text>
                    <text x="500" y="940" font-size="11">• Blanket size: 45×35 cm</text>
                    <text x="500" y="955" font-size="11">• Direct contact therapy</text>
                    <text x="500" y="970" font-size="11">• Flexible positioning</text>

                    <text x="800" y="910" font-size="12" font-weight="bold">Safety Features:</text>
                    <text x="800" y="925" font-size="11">• Eye protection required</text>
                    <text x="800" y="940" font-size="11">• Temperature monitoring</text>
                    <text x="800" y="955" font-size="11">• Automatic shutoff</text>
                    <text x="800" y="970" font-size="11">• UV filtering</text>
                </g>

                <!-- Connection lines -->
                <path d="M 300 350 L 300 720" stroke="#2196f3" stroke-width="3" fill="none" marker-end="url(#arrow2)"/>
                <path d="M 800 460 L 800 720" stroke="#ff9800" stroke-width="3" fill="none" marker-end="url(#arrow2)"/>

                <!-- Gradient definition for spectrum -->
                <defs>
                    <linearGradient id="spectrum" x1="0%" y1="0%" x2="100%" y2="0%">
                        <stop offset="0%" style="stop-color:#8b00ff;stop-opacity:1" />
                        <stop offset="20%" style="stop-color:#0000ff;stop-opacity:1" />
                        <stop offset="40%" style="stop-color:#00bfff;stop-opacity:1" />
                        <stop offset="60%" style="stop-color:#00ff00;stop-opacity:1" />
                        <stop offset="80%" style="stop-color:#ffff00;stop-opacity:1" />
                        <stop offset="100%" style="stop-color:#ff0000;stop-opacity:1" />
                    </linearGradient>
                    <marker id="arrow2" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                        <polygon points="0 0, 10 3.5, 0 7" fill="#333"/>
                    </marker>
                </defs>
            </svg>
        </div>
